import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/booking_service.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/common/empty_state.dart';

/// Provider for booking service
final bookingServiceProvider =
    Provider<BookingService>((ref) => BookingService());

/// Provider for user bookings
final userBookingsProvider = FutureProvider<List<Booking>>((ref) async {
  final bookingService = ref.read(bookingServiceProvider);
  return await bookingService.getUserBookings();
});

class BookingsScreen extends ConsumerWidget {
  const BookingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bookingsAsync = ref.watch(userBookingsProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Bookings'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.refresh(userBookingsProvider),
            tooltip: 'Refresh bookings',
          ),
        ],
      ),
      body: bookingsAsync.when(
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Failed to load bookings',
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.refresh(userBookingsProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        data: (bookings) => bookings.isEmpty
            ? const EmptyState(
                title: 'No Bookings Yet',
                message:
                    'Your bookings will appear here once you make your first reservation.',
                icon: Icons.event_note,
                actionText: 'Explore Experiences',
              )
            : _buildBookingsList(context, bookings),
      ),
    );
  }

  Widget _buildBookingsList(BuildContext context, List<Booking> bookings) {
    final theme = Theme.of(context);

    // Group bookings by status
    final upcomingBookings = bookings
        .where((b) =>
            b.status == BookingStatus.confirmed &&
            b.date.isAfter(DateTime.now()))
        .toList();
    final pastBookings = bookings
        .where((b) =>
            b.status == BookingStatus.completed ||
            b.date.isBefore(DateTime.now()))
        .toList();
    final pendingBookings =
        bookings.where((b) => b.status == BookingStatus.pending).toList();

    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          TabBar(
            labelColor: theme.colorScheme.primary,
            unselectedLabelColor: theme.colorScheme.onSurface.withAlpha(153),
            indicatorColor: theme.colorScheme.primary,
            tabs: [
              Tab(
                text: 'Upcoming (${upcomingBookings.length})',
                icon: const Icon(Icons.schedule),
              ),
              Tab(
                text: 'Pending (${pendingBookings.length})',
                icon: const Icon(Icons.hourglass_empty),
              ),
              Tab(
                text: 'Past (${pastBookings.length})',
                icon: const Icon(Icons.history),
              ),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildBookingTab(context, upcomingBookings, 'upcoming'),
                _buildBookingTab(context, pendingBookings, 'pending'),
                _buildBookingTab(context, pastBookings, 'past'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingTab(
      BuildContext context, List<Booking> bookings, String type) {
    if (bookings.isEmpty) {
      String message;
      String actionText;
      switch (type) {
        case 'upcoming':
          message = 'No upcoming bookings. Book an experience to see it here.';
          actionText = 'Browse Experiences';
          break;
        case 'pending':
          message =
              'No pending bookings. Your booking requests will appear here.';
          actionText = 'Make a Booking';
          break;
        case 'past':
          message =
              'No past bookings yet. Your completed experiences will appear here.';
          actionText = 'Explore Now';
          break;
        default:
          message = 'No bookings found.';
          actionText = 'Get Started';
      }

      return EmptyState(
        title: 'No ${type.capitalize()} Bookings',
        message: message,
        icon: type == 'upcoming'
            ? Icons.event
            : type == 'pending'
                ? Icons.pending_actions
                : Icons.history,
        actionText: actionText,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) =>
          _buildBookingCard(context, bookings[index]),
    );
  }

  Widget _buildBookingCard(BuildContext context, Booking booking) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');
    final timeFormat = DateFormat('HH:mm');

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (booking.status) {
      case BookingStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = 'Pending';
        break;
      case BookingStatus.confirmed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'Confirmed';
        break;
      case BookingStatus.completed:
        statusColor = Colors.blue;
        statusIcon = Icons.done_all;
        statusText = 'Completed';
        break;
      case BookingStatus.cancelled:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'Cancelled';
        break;
      case BookingStatus.refunded:
        statusColor = Colors.purple;
        statusIcon = Icons.money_off;
        statusText = 'Refunded';
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Experience #${booking.experienceId}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withAlpha(26),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.calendar_today,
                    size: 16,
                    color: theme.colorScheme.onSurface.withAlpha(153)),
                const SizedBox(width: 8),
                Text(
                  dateFormat.format(booking.date),
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time,
                    size: 16,
                    color: theme.colorScheme.onSurface.withAlpha(153)),
                const SizedBox(width: 8),
                Text(
                  '${timeFormat.format(booking.timeSlot.startTime)} - ${timeFormat.format(booking.timeSlot.endTime)}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.people,
                    size: 16,
                    color: theme.colorScheme.onSurface.withAlpha(153)),
                const SizedBox(width: 8),
                Text(
                  '${booking.participantCount} ${booking.participantCount == 1 ? 'participant' : 'participants'}',
                  style: theme.textTheme.bodyMedium,
                ),
                const Spacer(),
                Text(
                  '\$${booking.totalAmount.toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            if (booking.specialRequirements.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.note,
                      size: 16,
                      color: theme.colorScheme.onSurface.withAlpha(153)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      booking.specialRequirements,
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
