import 'dart:io';
import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/providers/travel/document/document_providers.dart';
import 'package:culture_connect/services/storage_service.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/theme/app_colors.dart';

import 'package:culture_connect/widgets/travel/document/document_widgets.dart';
import 'package:provider/provider.dart';

/// A screen for uploading travel documents
class DocumentUploadScreen extends StatefulWidget {
  /// The type of document to upload
  final TravelDocumentType documentType;

  /// The document to edit (null for new documents)
  final TravelDocument? document;

  /// Creates a new document upload screen
  const DocumentUploadScreen({
    super.key,
    required this.documentType,
    this.document,
  });

  @override
  State<DocumentUploadScreen> createState() => _DocumentUploadScreenState();
}

class _DocumentUploadScreenState extends State<DocumentUploadScreen> {
  bool _isLoading = false;
  final LoggingService _loggingService = LoggingService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.document == null
            ? 'Add ${widget.documentType.displayName}'
            : 'Edit ${widget.document!.name}'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: DocumentUploadForm(
                documentType: widget.documentType,
                document: widget.document,
                onSubmit: _handleSubmit,
              ),
            ),
    );
  }

  /// Handle form submission
  Future<void> _handleSubmit(TravelDocument document, List<File> images) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final documentProvider =
          Provider.of<TravelDocumentProvider>(context, listen: false);
      final reminderProvider =
          Provider.of<DocumentReminderProvider>(context, listen: false);
      final storageService = StorageService(loggingService: _loggingService);

      // Upload images if there are any
      List<String> imageUrls = document.documentImageUrls;
      if (images.isNotEmpty) {
        final uploadedUrls = await _uploadImages(images, storageService);
        imageUrls = [...document.documentImageUrls, ...uploadedUrls];
      }

      // Update the document with the image URLs
      TravelDocument updatedDocument;
      if (document is Passport) {
        updatedDocument = document.copyWith(
          documentImageUrls: imageUrls,
        );
      } else if (document is Visa) {
        updatedDocument = document.copyWith(
          documentImageUrls: imageUrls,
        );
      } else if (document is IdCard) {
        updatedDocument = document.copyWith(
          documentImageUrls: imageUrls,
        );
      } else {
        updatedDocument = document;
      }

      // Save the document
      if (widget.document == null) {
        // Add a new document
        if (updatedDocument is Passport) {
          await documentProvider.addPassport(updatedDocument);
        } else if (updatedDocument is Visa) {
          await documentProvider.addVisa(updatedDocument);
        } else if (updatedDocument is IdCard) {
          await documentProvider.addIdCard(updatedDocument);
        }
      } else {
        // Update an existing document
        await documentProvider.updateDocument(updatedDocument);

        // Update reminders
        await reminderProvider.updateDocumentReminders(updatedDocument);
      }

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.document == null
                ? 'Document added'
                : 'Document updated'),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// Upload images to storage
  Future<List<String>> _uploadImages(
      List<File> images, StorageService storageService) async {
    final imageUrls = <String>[];

    for (final image in images) {
      try {
        final url = await storageService.uploadFile(
          file: image,
          path: 'documents/${widget.documentType.toString().split('.').last}',
        );

        if (url != null) {
          imageUrls.add(url);
        }
      } catch (e, stackTrace) {
        // Continue with other images if one fails
        _loggingService.error(
          'DocumentUploadScreen',
          'Failed to upload image',
          e,
          stackTrace,
        );
      }
    }

    return imageUrls;
  }
}
