// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/flight/flight_booking_management.dart';
import 'package:culture_connect/models/travel/flight/booking_info.dart';
import 'package:culture_connect/models/travel/flight/passenger_info.dart';
import 'package:culture_connect/services/flight/flight_management_service.dart';

import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/mascot_widget.dart';
import 'package:culture_connect/widgets/travel/flight/seat_change_dialog.dart';
import 'package:culture_connect/widgets/travel/flight/meal_preference_dialog.dart';
import 'package:culture_connect/widgets/travel/flight/special_assistance_dialog.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/travel/flight/flight_status_tracking_screen.dart';

/// Screen for managing flight bookings
class FlightBookingManagementScreen extends ConsumerStatefulWidget {
  /// Booking reference to manage
  final String bookingReference;

  /// Creates a new flight booking management screen
  const FlightBookingManagementScreen({
    super.key,
    required this.bookingReference,
  });

  @override
  ConsumerState<FlightBookingManagementScreen> createState() =>
      _FlightBookingManagementScreenState();
}

class _FlightBookingManagementScreenState
    extends ConsumerState<FlightBookingManagementScreen>
    with TickerProviderStateMixin {
  BookingInfo? _booking;
  List<BookingModification> _modifications = [];
  List<BoardingPass> _boardingPasses = [];
  bool _isLoading = true;
  String? _errorMessage;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadBookingData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Manage Booking',
        showBackButton: true,
        actions: [
          if (_booking != null)
            IconButton(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              tooltip: 'Refresh',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorView(theme)
              : _booking == null
                  ? _buildNotFoundView(theme)
                  : _buildBookingContent(theme),
    );
  }

  /// Build error view
  Widget _buildErrorView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const MascotWidget(
            size: 80,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Oops! Something went wrong',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            _errorMessage!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          ElevatedButton(
            onPressed: _loadBookingData,
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  /// Build not found view
  Widget _buildNotFoundView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const MascotWidget(
            size: 80,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          Text(
            'Booking Not Found',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'We couldn\'t find a booking with reference ${widget.bookingReference}',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  /// Build booking content
  Widget _buildBookingContent(ThemeData theme) {
    return Column(
      children: [
        // Booking header
        _buildBookingHeader(theme),

        // Tab bar
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.info_outline)),
            Tab(text: 'Status', icon: Icon(Icons.flight)),
            Tab(text: 'Boarding', icon: Icon(Icons.airplane_ticket)),
          ],
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(theme),
              _buildStatusTab(theme),
              _buildBoardingTab(theme),
            ],
          ),
        ),
      ],
    );
  }

  /// Build booking header
  Widget _buildBookingHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204), // 0.8 opacity
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _booking!.flight.flightNumber,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${_booking!.flight.departureAirport} → ${_booking!.flight.arrivalAirport}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white.withAlpha(230), // 0.9 opacity
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(51), // 0.2 opacity
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Text(
                  widget.bookingReference,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSmall),
          Text(
            _booking!.flight.formattedDepartureTime,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.white.withAlpha(230), // 0.9 opacity
            ),
          ),
        ],
      ),
    );
  }

  /// Build overview tab
  Widget _buildOverviewTab(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFlightDetailsCard(theme),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildPassengersCard(theme),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildModificationsCard(theme),
          const SizedBox(height: AppTheme.spacingMedium),
          _buildActionsCard(theme),
        ],
      ),
    );
  }

  /// Build status tab
  Widget _buildStatusTab(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          ElevatedButton.icon(
            onPressed: _navigateToStatusTracking,
            icon: const Icon(Icons.track_changes),
            label: const Text('Track Flight Status'),
          ),
          const SizedBox(height: AppTheme.spacingMedium),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const MascotWidget(
                    size: 80,
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),
                  Text(
                    'Real-time Flight Tracking',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingMedium),
                  Text(
                    'Get live updates on your flight status, gate changes, and delays.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build boarding tab
  Widget _buildBoardingTab(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      child: Column(
        children: [
          if (_boardingPasses.isEmpty) ...[
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const MascotWidget(
                      size: 80,
                    ),
                    const SizedBox(height: AppTheme.spacingLarge),
                    Text(
                      'Boarding Pass Available Soon',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingMedium),
                    Text(
                      'Your boarding pass will be available 24 hours before departure.',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppTheme.spacingLarge),
                    ElevatedButton.icon(
                      onPressed: _generateBoardingPasses,
                      icon: const Icon(Icons.airplane_ticket),
                      label: const Text('Generate Boarding Pass'),
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            Expanded(
              child: ListView.builder(
                itemCount: _boardingPasses.length,
                itemBuilder: (context, index) {
                  return _buildBoardingPassCard(theme, _boardingPasses[index]);
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build flight details card
  Widget _buildFlightDetailsCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Flight Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            _buildDetailRow(theme, 'Flight', _booking!.flight.flightNumber),
            _buildDetailRow(theme, 'Route',
                '${_booking!.flight.departureAirport} → ${_booking!.flight.arrivalAirport}'),
            _buildDetailRow(
                theme, 'Departure', _booking!.flight.formattedDepartureTime),
            _buildDetailRow(
                theme, 'Arrival', _booking!.flight.formattedArrivalTime),
            _buildDetailRow(
                theme, 'Duration', _booking!.flight.formattedDuration),
          ],
        ),
      ),
    );
  }

  /// Build passengers card
  Widget _buildPassengersCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Passengers (${_booking!.passengers.length})',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            ...(_booking!.passengers.map((passenger) => Padding(
                  padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
                  child: Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: AppTheme.spacingSmall),
                      Expanded(
                        child: Text(
                          '${passenger.firstName} ${passenger.lastName}',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ),
                      if (_booking!.selectedSeats
                          .containsKey(passenger.passportNumber))
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary
                                .withAlpha(26), // 0.1 opacity
                            borderRadius: BorderRadius.circular(
                                AppTheme.borderRadiusSmall),
                          ),
                          child: Text(
                            'Seat ${_booking!.selectedSeats[passenger.passportNumber]}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ))),
          ],
        ),
      ),
    );
  }

  /// Build modifications card
  Widget _buildModificationsCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Modifications',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            if (_modifications.isEmpty)
              Text(
                'No modifications made',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              )
            else
              ...(_modifications.take(3).map((mod) => Padding(
                    padding:
                        const EdgeInsets.only(bottom: AppTheme.spacingSmall),
                    child: Row(
                      children: [
                        Icon(
                          mod.type.icon,
                          size: 16,
                          color: mod.isApproved ? Colors.green : Colors.orange,
                        ),
                        const SizedBox(width: AppTheme.spacingSmall),
                        Expanded(
                          child: Text(
                            mod.description,
                            style: theme.textTheme.bodySmall,
                          ),
                        ),
                        Text(
                          mod.isApproved ? 'Approved' : 'Pending',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color:
                                mod.isApproved ? Colors.green : Colors.orange,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ))),
          ],
        ),
      ),
    );
  }

  /// Build actions card
  Widget _buildActionsCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Wrap(
              spacing: AppTheme.spacingSmall,
              runSpacing: AppTheme.spacingSmall,
              children: [
                _buildActionChip(theme, 'Change Seat',
                    Icons.airline_seat_recline_normal, _changeSeat),
                _buildActionChip(
                    theme, 'Meal Preference', Icons.restaurant, _changeMeal),
                _buildActionChip(theme, 'Special Assistance', Icons.accessible,
                    _requestAssistance),
                _buildActionChip(
                    theme, 'Cancel Booking', Icons.cancel, _cancelBooking),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build action chip
  Widget _buildActionChip(
      ThemeData theme, String label, IconData icon, VoidCallback onTap) {
    return ActionChip(
      avatar: Icon(icon, size: 16),
      label: Text(label),
      onPressed: onTap,
    );
  }

  /// Build detail row
  Widget _buildDetailRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build boarding pass card
  Widget _buildBoardingPassCard(ThemeData theme, BoardingPass pass) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${pass.passenger.firstName} ${pass.passenger.lastName}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Seat ${pass.seatNumber ?? 'TBA'}',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    border: Border.all(color: theme.colorScheme.outline),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: const Center(
                    child: Text('QR', style: TextStyle(fontSize: 12)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            ElevatedButton.icon(
              onPressed: () => _showBoardingPass(pass),
              icon: const Icon(Icons.airplane_ticket),
              label: const Text('View Boarding Pass'),
            ),
          ],
        ),
      ),
    );
  }

  /// Load booking data
  Future<void> _loadBookingData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final service = ref.read(flightManagementServiceProvider);

      final booking = await service.getBooking(widget.bookingReference);
      final modifications =
          await service.getBookingModifications(widget.bookingReference);
      final boardingPasses =
          await service.getBoardingPasses(widget.bookingReference);

      if (mounted) {
        setState(() {
          _booking = booking;
          _modifications = modifications;
          _boardingPasses = boardingPasses;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load booking: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// Refresh data
  Future<void> _refreshData() async {
    await HapticFeedback.selectionClick();
    await _loadBookingData();
  }

  /// Navigate to status tracking
  void _navigateToStatusTracking() {
    if (_booking != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FlightStatusTrackingScreen(
            flightNumber: _booking!.flight.flightNumber,
            departureDate: _booking!.flight.departureDateTime,
          ),
        ),
      );
    }
  }

  /// Generate boarding passes
  Future<void> _generateBoardingPasses() async {
    if (_booking == null) return;

    try {
      final service = ref.read(flightManagementServiceProvider);

      for (final passenger in _booking!.passengers) {
        await service.generateBoardingPass(
          bookingReference: widget.bookingReference,
          passenger: passenger,
          gate: 'A12', // Mock gate
          terminal: 'Terminal 1',
          boardingGroup: 'Group A',
        );
      }

      await _loadBookingData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Boarding passes generated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate boarding passes: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show boarding pass
  void _showBoardingPass(BoardingPass pass) {
    // TODO: Navigate to full boarding pass screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Boarding pass for ${pass.passenger.firstName} ${pass.passenger.lastName}'),
      ),
    );
  }

  /// Change seat
  void _changeSeat() {
    if (_booking == null) return;

    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => SeatChangeDialog(
        booking: _booking!,
        onSeatChanged: (passenger, newSeat) {
          _handleSeatChange(passenger, newSeat);
        },
      ),
    );
  }

  /// Change meal
  void _changeMeal() {
    if (_booking == null) return;

    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => MealPreferenceDialog(
        booking: _booking!,
        onMealChanged: (passenger, newMeal) {
          _handleMealChange(passenger, newMeal);
        },
      ),
    );
  }

  /// Request assistance
  void _requestAssistance() {
    if (_booking == null) return;

    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => SpecialAssistanceDialog(
        booking: _booking!,
        onAssistanceChanged: (passenger, assistance, details) {
          _handleAssistanceChange(passenger, assistance, details);
        },
      ),
    );
  }

  /// Cancel booking
  void _cancelBooking() {
    _showCancellationDialog();
  }

  /// Show booking cancellation dialog
  void _showCancellationDialog() {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Booking'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to cancel this booking?'),
            const SizedBox(height: 16),
            const Text(
              'Cancellation Policy:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Free cancellation up to 24 hours before departure\n'
              '• 50% refund for cancellations within 24 hours\n'
              '• No refund for no-shows',
              style: TextStyle(fontSize: 12),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for cancellation (optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Keep Booking'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processCancellation(reasonController.text.trim());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cancel Booking'),
          ),
        ],
      ),
    );
  }

  /// Process booking cancellation
  Future<void> _processCancellation(String reason) async {
    if (_booking == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // Use the flight management service to cancel the booking
      final flightManagementService = ref.read(flightManagementServiceProvider);
      final success = await flightManagementService.cancelBooking(
        widget.bookingReference,
        reason.isEmpty ? 'User requested cancellation' : reason,
      );

      if (success && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Booking cancelled successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh booking data to show updated status
        await _loadBookingData();
      } else if (mounted) {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to cancel booking. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error cancelling booking: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Handle seat change
  void _handleSeatChange(PassengerInfo passenger, String newSeat) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Seat changed to $newSeat for ${passenger.firstName} ${passenger.lastName}'),
        backgroundColor: Colors.green,
      ),
    );
    // Reload booking data to reflect changes
    _loadBookingData();
  }

  /// Handle meal preference change
  void _handleMealChange(PassengerInfo passenger, MealPreference newMeal) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            'Meal preference updated to ${newMeal.displayName} for ${passenger.firstName} ${passenger.lastName}'),
        backgroundColor: Colors.blue,
      ),
    );
    // Reload booking data to reflect changes
    _loadBookingData();
  }

  /// Handle special assistance change
  void _handleAssistanceChange(
      PassengerInfo passenger, bool assistance, String details) {
    final message = assistance
        ? 'Special assistance requested for ${passenger.firstName} ${passenger.lastName}'
        : 'Special assistance removed for ${passenger.firstName} ${passenger.lastName}';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
      ),
    );
    // Reload booking data to reflect changes
    _loadBookingData();
  }
}
