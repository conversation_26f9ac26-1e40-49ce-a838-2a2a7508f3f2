// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/travel/flight/flight_info.dart';
import 'package:culture_connect/services/flight/flight_management_service.dart';
import 'package:culture_connect/services/flight/flight_status_notification_service.dart';
import 'package:culture_connect/providers/flight/flight_status_notification_provider.dart';
import 'package:culture_connect/widgets/flight/flight_status_widget.dart';
import 'package:culture_connect/widgets/common/custom_app_bar.dart';
import 'package:culture_connect/widgets/common/mascot_widget.dart';
import 'package:culture_connect/widgets/travel/flight/flight_support_contact_dialog.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Screen for tracking real-time flight status
class FlightStatusTrackingScreen extends ConsumerStatefulWidget {
  /// Flight number to track
  final String flightNumber;

  /// Departure date
  final DateTime departureDate;

  /// Creates a new flight status tracking screen
  const FlightStatusTrackingScreen({
    super.key,
    required this.flightNumber,
    required this.departureDate,
  });

  @override
  ConsumerState<FlightStatusTrackingScreen> createState() =>
      _FlightStatusTrackingScreenState();
}

class _FlightStatusTrackingScreenState
    extends ConsumerState<FlightStatusTrackingScreen>
    with TickerProviderStateMixin {
  FlightInfo? _flightInfo;
  bool _isLoading = true;
  String? _errorMessage;
  bool _autoRefresh = true;
  FlightNotificationSettings? _notificationSettings;
  bool _isTrackingFlight = false;

  late AnimationController _refreshController;
  late Animation<double> _refreshAnimation;

  @override
  void initState() {
    super.initState();

    _refreshController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _refreshAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _refreshController,
      curve: Curves.easeInOut,
    ));

    _loadFlightStatus();
    _startAutoRefresh();
    _loadNotificationSettings();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Flight Status',
        showBackButton: true,
        actions: [
          IconButton(
            onPressed: _refreshFlightStatus,
            icon: AnimatedBuilder(
              animation: _refreshAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _refreshAnimation.value * 2 * 3.14159,
                  child: const Icon(Icons.refresh),
                );
              },
            ),
            tooltip: 'Refresh Status',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'auto_refresh',
                child: Row(
                  children: [
                    Icon(
                      _autoRefresh ? Icons.pause : Icons.play_arrow,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(_autoRefresh
                        ? 'Pause Auto-refresh'
                        : 'Enable Auto-refresh'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'tracking',
                child: Row(
                  children: [
                    Icon(
                      _isTrackingFlight
                          ? Icons.notifications_active
                          : Icons.notifications_off,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(_isTrackingFlight
                        ? 'Disable Flight Tracking'
                        : 'Enable Flight Tracking'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'notifications',
                child: Row(
                  children: [
                    Icon(Icons.notifications, size: 20),
                    SizedBox(width: 8),
                    Text('Notification Settings'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingView(theme)
          : _errorMessage != null
              ? _buildErrorView(theme)
              : _flightInfo == null
                  ? _buildNotFoundView(theme)
                  : _buildFlightStatusContent(theme),
    );
  }

  /// Build loading view
  Widget _buildLoadingView(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const MascotWidget(
            size: 80,
          ),
          const SizedBox(height: AppTheme.spacingLarge),
          const CircularProgressIndicator(),
          const SizedBox(height: AppTheme.spacingMedium),
          Text(
            'Tracking flight ${widget.flightNumber}...',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error view
  Widget _buildErrorView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const MascotWidget(
              size: 80,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            Text(
              'Unable to Track Flight',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              _errorMessage!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            ElevatedButton.icon(
              onPressed: _loadFlightStatus,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build not found view
  Widget _buildNotFoundView(ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const MascotWidget(
              size: 80,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            Text(
              'Flight Not Found',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'We couldn\'t find flight ${widget.flightNumber} for ${_formatDate(widget.departureDate)}',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.spacingLarge),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build flight status content
  Widget _buildFlightStatusContent(ThemeData theme) {
    return RefreshIndicator(
      onRefresh: _refreshFlightStatus,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main status widget
            FlightStatusWidget(
              flightInfo: _flightInfo!,
              variant: FlightStatusVariant.full,
              showRefresh: false,
              onTap: _showStatusDetails,
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // Timeline view
            FlightStatusWidget(
              flightInfo: _flightInfo!,
              variant: FlightStatusVariant.timeline,
            ),

            const SizedBox(height: AppTheme.spacingMedium),

            // Additional information
            _buildAdditionalInfo(theme),

            const SizedBox(height: AppTheme.spacingMedium),

            // Weather information
            _buildWeatherInfo(theme),

            const SizedBox(height: AppTheme.spacingMedium),

            // Alternative flights (if delayed/cancelled)
            if (_flightInfo!.status == FlightStatus.delayed ||
                _flightInfo!.status == FlightStatus.cancelled)
              _buildAlternativeFlights(theme),

            const SizedBox(height: AppTheme.spacingMedium),

            // Auto-refresh indicator
            _buildAutoRefreshIndicator(theme),
          ],
        ),
      ),
    );
  }

  /// Build additional information card
  Widget _buildAdditionalInfo(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Flight Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            _buildInfoRow(theme, 'Aircraft', 'Boeing 777'),
            _buildInfoRow(theme, 'Airline', _flightInfo!.airlineName),
            if (_flightInfo!.departureGate != null)
              _buildInfoRow(theme, 'Gate', _flightInfo!.departureGate!),
            if (_flightInfo!.departureTerminal != null)
              _buildInfoRow(theme, 'Terminal', _flightInfo!.departureTerminal!),
            _buildInfoRow(theme, 'Status', _flightInfo!.status.displayName),
          ],
        ),
      ),
    );
  }

  /// Build weather information card
  Widget _buildWeatherInfo(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weather Conditions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Row(
              children: [
                Expanded(
                  child: _buildWeatherLocation(
                    theme,
                    _flightInfo!.departureAirportCode,
                    'Departure',
                    '22°C', // Mock weather data
                    'Partly Cloudy',
                    Icons.wb_cloudy,
                  ),
                ),
                const SizedBox(width: AppTheme.spacingMedium),
                Expanded(
                  child: _buildWeatherLocation(
                    theme,
                    _flightInfo!.arrivalAirportCode,
                    'Arrival',
                    '18°C', // Mock weather data
                    'Clear',
                    Icons.wb_sunny,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build weather location info
  Widget _buildWeatherLocation(
    ThemeData theme,
    String airport,
    String label,
    String temperature,
    String condition,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          airport,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppTheme.spacingSmall),
        Row(
          children: [
            Icon(icon, size: 20, color: Colors.orange),
            const SizedBox(width: 4),
            Text(
              temperature,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Text(
          condition,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Build alternative flights card
  Widget _buildAlternativeFlights(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Alternative Flights',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            Text(
              'We found alternative flights for your route:',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            // Mock alternative flights
            _buildAlternativeFlight(theme, 'BA456', '14:30', '20:45'),
            _buildAlternativeFlight(theme, 'LH789', '16:15', '22:30'),
            const SizedBox(height: AppTheme.spacingMedium),
            ElevatedButton.icon(
              onPressed: _contactSupport,
              icon: const Icon(Icons.support_agent),
              label: const Text('Contact Support'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build alternative flight item
  Widget _buildAlternativeFlight(
      ThemeData theme, String flightNumber, String departure, String arrival) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        children: [
          Icon(
            Icons.flight,
            size: 16,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            flightNumber,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),
          Text(
            '$departure - $arrival',
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  /// Build auto-refresh indicator
  Widget _buildAutoRefreshIndicator(ThemeData theme) {
    if (!_autoRefresh) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingSmall),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withAlpha(26), // 0.1 opacity
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor:
                  AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
            ),
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Text(
            'Auto-refreshing every 30 seconds',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build info row
  Widget _buildInfoRow(ThemeData theme, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Load flight status
  Future<void> _loadFlightStatus() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final service = ref.read(flightManagementServiceProvider);
      final flightInfo = await service.getFlightStatus(
          widget.flightNumber, widget.departureDate);

      if (mounted) {
        setState(() {
          _flightInfo = flightInfo;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load flight status: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// Refresh flight status
  Future<void> _refreshFlightStatus() async {
    await HapticFeedback.selectionClick();
    _refreshController.forward().then((_) => _refreshController.reset());
    await _loadFlightStatus();
  }

  /// Start auto-refresh
  void _startAutoRefresh() {
    if (_autoRefresh) {
      Future.delayed(const Duration(seconds: 30), () {
        if (mounted && _autoRefresh) {
          _loadFlightStatus();
          _startAutoRefresh();
        }
      });
    }
  }

  /// Load notification settings
  Future<void> _loadNotificationSettings() async {
    try {
      final settings = await ref.read(
        flightNotificationSettingsProvider(
          FlightSettingsParams(
            flightNumber: widget.flightNumber,
            departureDate: widget.departureDate,
          ),
        ).future,
      );

      if (mounted) {
        setState(() {
          _notificationSettings =
              settings ?? FlightNotificationSettings.defaultSettings();
        });
      }
    } catch (e) {
      // Use default settings if loading fails
      if (mounted) {
        setState(() {
          _notificationSettings = FlightNotificationSettings.defaultSettings();
        });
      }
    }
  }

  /// Enable flight tracking with notifications
  Future<void> _enableFlightTracking() async {
    try {
      await ref.read(
        trackFlightProvider(
          TrackFlightParams(
            flightNumber: widget.flightNumber,
            departureDate: widget.departureDate,
            bookingReference: 'TRACK_${widget.flightNumber}',
            settings: _notificationSettings,
          ),
        ).future,
      );

      if (mounted) {
        setState(() {
          _isTrackingFlight = true;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Flight tracking enabled'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to enable tracking: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Disable flight tracking
  Future<void> _disableFlightTracking() async {
    try {
      await ref.read(
        stopTrackingFlightProvider(
          StopTrackingParams(
            flightNumber: widget.flightNumber,
            departureDate: widget.departureDate,
          ),
        ).future,
      );

      if (mounted) {
        setState(() {
          _isTrackingFlight = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Flight tracking disabled'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to disable tracking: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Handle menu selection
  void _handleMenuSelection(String value) {
    switch (value) {
      case 'auto_refresh':
        setState(() {
          _autoRefresh = !_autoRefresh;
        });
        if (_autoRefresh) {
          _startAutoRefresh();
        }
        break;
      case 'tracking':
        if (_isTrackingFlight) {
          _disableFlightTracking();
        } else {
          _enableFlightTracking();
        }
        break;
      case 'notifications':
        _showNotificationSettings();
        break;
    }
  }

  /// Show status details
  void _showStatusDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Flight ${widget.flightNumber}'),
        content: Text('Status: ${_flightInfo!.status.displayName}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show notification settings
  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => _buildNotificationSettingsDialog(),
    );
  }

  /// Build notification settings dialog
  Widget _buildNotificationSettingsDialog() {
    final theme = Theme.of(context);
    final settings =
        _notificationSettings ?? FlightNotificationSettings.defaultSettings();

    // Use real settings from the notification service
    bool notificationsEnabled = settings.notificationsEnabled;
    bool statusChanges = settings.statusChanges;
    bool gateChanges = settings.gateChanges;
    bool boarding = settings.boarding;
    bool departure = settings.departure;
    bool arrival = settings.arrival;
    int reminderMinutes = settings.reminderMinutes;

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.notifications,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text('Flight Notifications'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Master toggle
                  SwitchListTile(
                    title: const Text('Enable Notifications'),
                    subtitle: const Text('Receive flight status updates'),
                    value: notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        notificationsEnabled = value;
                      });
                    },
                  ),

                  if (notificationsEnabled) ...[
                    const Divider(),

                    // Notification types
                    Text(
                      'Notification Types',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),

                    SwitchListTile(
                      title: const Text('Status Changes'),
                      subtitle: const Text('Delays, cancellations, diversions'),
                      value: statusChanges,
                      onChanged: (value) {
                        setState(() {
                          statusChanges = value;
                        });
                      },
                    ),

                    SwitchListTile(
                      title: const Text('Gate Changes'),
                      subtitle: const Text('Terminal and gate updates'),
                      value: gateChanges,
                      onChanged: (value) {
                        setState(() {
                          gateChanges = value;
                        });
                      },
                    ),

                    SwitchListTile(
                      title: const Text('Boarding Alerts'),
                      subtitle: const Text('When boarding begins'),
                      value: boarding,
                      onChanged: (value) {
                        setState(() {
                          boarding = value;
                        });
                      },
                    ),

                    SwitchListTile(
                      title: const Text('Departure Alerts'),
                      subtitle: const Text('When flight departs'),
                      value: departure,
                      onChanged: (value) {
                        setState(() {
                          departure = value;
                        });
                      },
                    ),

                    SwitchListTile(
                      title: const Text('Arrival Alerts'),
                      subtitle: const Text('When flight arrives'),
                      value: arrival,
                      onChanged: (value) {
                        setState(() {
                          arrival = value;
                        });
                      },
                    ),

                    const Divider(),

                    // Reminder timing
                    Text(
                      'Reminder Timing',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),

                    ListTile(
                      title: const Text('Departure Reminder'),
                      subtitle:
                          Text('$reminderMinutes minutes before departure'),
                      trailing: const Icon(Icons.chevron_right),
                      onTap: () {
                        _showReminderTimeDialog(context, reminderMinutes,
                            (newValue) {
                          setState(() {
                            reminderMinutes = newValue;
                          });
                        });
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                // Save settings using the notification service
                final newSettings = FlightNotificationSettings(
                  notificationsEnabled: notificationsEnabled,
                  statusChanges: statusChanges,
                  gateChanges: gateChanges,
                  boarding: boarding,
                  departure: departure,
                  arrival: arrival,
                  reminderMinutes: reminderMinutes,
                );

                try {
                  await ref.read(
                    updateFlightNotificationSettingsProvider(
                      UpdateSettingsParams(
                        flightNumber: widget.flightNumber,
                        departureDate: widget.departureDate,
                        settings: newSettings,
                      ),
                    ).future,
                  );

                  // Update local state
                  this.setState(() {
                    _notificationSettings = newSettings;
                  });

                  // Enable tracking if notifications are enabled
                  if (notificationsEnabled && !_isTrackingFlight) {
                    await _enableFlightTracking();
                  }

                  navigator.pop();
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('Notification preferences saved'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  navigator.pop();
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Failed to save settings: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  /// Show reminder time selection dialog
  void _showReminderTimeDialog(
      BuildContext context, int currentMinutes, Function(int) onChanged) {
    final reminderOptions = [15, 30, 60, 120, 180]; // minutes

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Departure Reminder'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: reminderOptions.map((minutes) {
            String displayText;
            if (minutes < 60) {
              displayText = '$minutes minutes';
            } else {
              final hours = minutes ~/ 60;
              displayText = hours == 1 ? '1 hour' : '$hours hours';
            }

            return RadioListTile<int>(
              title: Text(displayText),
              value: minutes,
              groupValue: currentMinutes,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Contact support
  void _contactSupport() {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => FlightSupportContactDialog(
        flightNumber: widget.flightNumber,
        departureDate: widget.departureDate,
        flightInfo: _flightInfo,
      ),
    );
  }

  /// Format date
  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
